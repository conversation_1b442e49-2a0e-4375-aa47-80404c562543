import 'package:shared_preferences/shared_preferences.dart';
import '../models/game_level.dart';

class HighScoreService {
  static final HighScoreService _instance = HighScoreService._internal();
  factory HighScoreService() => _instance;
  HighScoreService._internal();

  SharedPreferences? _prefs;

  // Keys for different difficulty levels
  static const String _easyHighScoreKey = 'high_score_easy';
  static const String _mediumHighScoreKey = 'high_score_medium';
  static const String _hardHighScoreKey = 'high_score_hard';
  static const String _overallHighScoreKey = 'high_score_overall';

  // Initialize
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get high score for specific level
  Future<int> getHighScore(GameLevel level) async {
    if (_prefs == null) await init();
    
    String key = _getKeyForLevel(level);
    return _prefs?.getInt(key) ?? 0;
  }

  // Get overall high score (across all levels)
  Future<int> getOverallHighScore() async {
    if (_prefs == null) await init();
    return _prefs?.getInt(_overallHighScoreKey) ?? 0;
  }

  // Save high score for specific level
  Future<bool> saveHighScore(GameLevel level, int score) async {
    if (_prefs == null) await init();
    
    String key = _getKeyForLevel(level);
    int currentHighScore = await getHighScore(level);
    
    if (score > currentHighScore) {
      await _prefs?.setInt(key, score);
      
      // Also update overall high score if needed
      int overallHighScore = await getOverallHighScore();
      if (score > overallHighScore) {
        await _prefs?.setInt(_overallHighScoreKey, score);
      }
      
      return true; // New high score
    }
    
    return false; // Not a new high score
  }

  // Get all high scores
  Future<Map<GameLevel, int>> getAllHighScores() async {
    return {
      GameLevel.easy: await getHighScore(GameLevel.easy),
      GameLevel.medium: await getHighScore(GameLevel.medium),
      GameLevel.hard: await getHighScore(GameLevel.hard),
    };
  }

  // Reset all high scores
  Future<void> resetAllHighScores() async {
    if (_prefs == null) await init();
    
    await _prefs?.remove(_easyHighScoreKey);
    await _prefs?.remove(_mediumHighScoreKey);
    await _prefs?.remove(_hardHighScoreKey);
    await _prefs?.remove(_overallHighScoreKey);
  }

  // Helper method to get key for level
  String _getKeyForLevel(GameLevel level) {
    switch (level) {
      case GameLevel.easy:
        return _easyHighScoreKey;
      case GameLevel.medium:
        return _mediumHighScoreKey;
      case GameLevel.hard:
        return _hardHighScoreKey;
    }
  }

  // Check if score is a new high score without saving
  Future<bool> isNewHighScore(GameLevel level, int score) async {
    int currentHighScore = await getHighScore(level);
    return score > currentHighScore;
  }
}
