class Word {
  final int? id;
  final String word;
  final String meaning;
  final int length;
  final String category;

  Word({
    this.id,
    required this.word,
    required this.meaning,
    required this.length,
    this.category = 'general',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'word': word.toLowerCase(),
      'meaning': meaning,
      'length': length,
      'category': category,
    };
  }

  factory Word.fromMap(Map<String, dynamic> map) {
    return Word(
      id: map['id'],
      word: map['word'],
      meaning: map['meaning'],
      length: map['length'],
      category: map['category'] ?? 'general',
    );
  }

  @override
  String toString() {
    return 'Word{id: $id, word: $word, meaning: $meaning, length: $length, category: $category}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Word &&
          runtimeType == other.runtimeType &&
          word == other.word;

  @override
  int get hashCode => word.hashCode;
}
