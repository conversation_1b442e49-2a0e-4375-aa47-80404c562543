import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsService extends ChangeNotifier {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  SharedPreferences? _prefs;

  // Settings keys
  static const String _darkModeKey = 'dark_mode';
  static const String _soundEnabledKey = 'sound_enabled';
  static const String _hintsEnabledKey = 'hints_enabled';
  static const String _timedModeKey = 'timed_mode';
  static const String _vibrationEnabledKey = 'vibration_enabled';

  // Default values
  bool _isDarkMode = false;
  bool _isSoundEnabled = true;
  bool _areHintsEnabled = true;
  bool _isTimedModeDefault = false;
  bool _isVibrationEnabled = true;

  // Getters
  bool get isDarkMode => _isDarkMode;
  bool get isSoundEnabled => _isSoundEnabled;
  bool get areHintsEnabled => _areHintsEnabled;
  bool get isTimedModeDefault => _isTimedModeDefault;
  bool get isVibrationEnabled => _isVibrationEnabled;

  // Initialize settings
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _isDarkMode = _prefs!.getBool(_darkModeKey) ?? false;
    _isSoundEnabled = _prefs!.getBool(_soundEnabledKey) ?? true;
    _areHintsEnabled = _prefs!.getBool(_hintsEnabledKey) ?? true;
    _isTimedModeDefault = _prefs!.getBool(_timedModeKey) ?? false;
    _isVibrationEnabled = _prefs!.getBool(_vibrationEnabledKey) ?? true;

    notifyListeners();
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    await _prefs?.setBool(_darkModeKey, _isDarkMode);
    notifyListeners();
  }

  // Toggle sound
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    await _prefs?.setBool(_soundEnabledKey, _isSoundEnabled);
    notifyListeners();
  }

  // Toggle hints
  Future<void> toggleHints() async {
    _areHintsEnabled = !_areHintsEnabled;
    await _prefs?.setBool(_hintsEnabledKey, _areHintsEnabled);
    notifyListeners();
  }

  // Toggle timed mode default
  Future<void> toggleTimedModeDefault() async {
    _isTimedModeDefault = !_isTimedModeDefault;
    await _prefs?.setBool(_timedModeKey, _isTimedModeDefault);
    notifyListeners();
  }

  // Toggle vibration
  Future<void> toggleVibration() async {
    _isVibrationEnabled = !_isVibrationEnabled;
    await _prefs?.setBool(_vibrationEnabledKey, _isVibrationEnabled);
    notifyListeners();
  }

  // Reset all settings to default
  Future<void> resetToDefaults() async {
    _isDarkMode = false;
    _isSoundEnabled = true;
    _areHintsEnabled = true;
    _isTimedModeDefault = false;
    _isVibrationEnabled = true;

    await _prefs?.setBool(_darkModeKey, _isDarkMode);
    await _prefs?.setBool(_soundEnabledKey, _isSoundEnabled);
    await _prefs?.setBool(_hintsEnabledKey, _areHintsEnabled);
    await _prefs?.setBool(_timedModeKey, _isTimedModeDefault);
    await _prefs?.setBool(_vibrationEnabledKey, _isVibrationEnabled);

    notifyListeners();
  }
}
