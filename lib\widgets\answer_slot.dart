import 'package:flutter/material.dart';

class AnswerSlot extends StatelessWidget {
  final String? letter;
  final VoidCallback? onTap;

  const AnswerSlot({
    super.key,
    this.letter,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final hasLetter = letter != null && letter!.isNotEmpty;
    
    return GestureDetector(
      onTap: hasLetter ? onTap : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 50,
        height: 60,
        decoration: BoxDecoration(
          color: hasLetter 
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: hasLetter 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: hasLetter
              ? Text(
                  letter!.toUpperCase(),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                )
              : Container(
                  width: 20,
                  height: 2,
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                ),
        ),
      ),
    );
  }
}
