import 'package:flutter/material.dart';

class AnswerSlot extends StatelessWidget {
  final String? letter;
  final VoidCallback? onTap;

  const AnswerSlot({
    super.key,
    this.letter,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final hasLetter = letter != null && letter!.isNotEmpty;
    final screenWidth = MediaQuery.of(context).size.width;
    final slotSize = (screenWidth - 80) / 10; // Responsive size based on screen width
    final finalSize = slotSize.clamp(40.0, 60.0); // Min 40, max 60

    return GestureDetector(
      onTap: hasLetter ? onTap : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: finalSize,
        height: finalSize + 10,
        decoration: BoxDecoration(
          color: hasLetter
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: hasLetter
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: hasLetter
              ? Text(
                  letter!.toUpperCase(),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontSize: finalSize * 0.4, // Responsive font size
                  ),
                )
              : Container(
                  width: finalSize * 0.4,
                  height: 2,
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                ),
        ),
      ),
    );
  }
}
