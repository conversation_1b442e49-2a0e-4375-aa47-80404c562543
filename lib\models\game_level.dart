enum GameLevel {
  easy(minLength: 3, maxLength: 4, timeLimit: 60, name: '<PERSON><PERSON>'),
  medium(minLength: 5, maxLength: 6, timeLimit: 45, name: '<PERSON>rung bình'),
  hard(minLength: 7, maxLength: 10, timeLimit: 30, name: '<PERSON><PERSON><PERSON>');

  const GameLevel({
    required this.minLength,
    required this.maxLength,
    required this.timeLimit,
    required this.name,
  });

  final int minLength;
  final int maxLength;
  final int timeLimit; // seconds
  final String name;

  int get baseScore {
    switch (this) {
      case GameLevel.easy:
        return 10;
      case GameLevel.medium:
        return 20;
      case GameLevel.hard:
        return 30;
    }
  }
}
