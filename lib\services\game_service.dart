import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/word.dart';
import '../models/game_level.dart';
import '../models/game_stats.dart';
import 'database_service.dart';

class GameService extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  // Game state
  Word? _currentWord;
  List<String> _scrambledLetters = [];
  List<String> _playerAnswer = [];
  GameLevel _currentLevel = GameLevel.easy;
  int _score = 0;
  int _hintsUsed = 0;
  bool _isGameActive = false;
  bool _isTimedMode = false;
  int _timeRemaining = 0;
  Timer? _gameTimer;
  DateTime? _gameStartTime;

  // Getters
  Word? get currentWord => _currentWord;
  List<String> get scrambledLetters => List.unmodifiable(_scrambledLetters);
  List<String> get playerAnswer => List.unmodifiable(_playerAnswer);
  GameLevel get currentLevel => _currentLevel;
  int get score => _score;
  int get hintsUsed => _hintsUsed;
  bool get isGameActive => _isGameActive;
  bool get isTimedMode => _isTimedMode;
  int get timeRemaining => _timeRemaining;
  bool get isAnswerComplete => _playerAnswer.length == _currentWord?.word.length;
  bool get isAnswerCorrect => _playerAnswer.join().toLowerCase() == _currentWord?.word.toLowerCase();

  // Start new game
  Future<bool> startNewGame(GameLevel level, {bool timedMode = false}) async {
    _currentLevel = level;
    _isTimedMode = timedMode;
    _score = 0;
    _hintsUsed = 0;
    _gameStartTime = DateTime.now();
    
    if (_isTimedMode) {
      _timeRemaining = level.timeLimit;
      _startTimer();
    }
    
    return await _loadNextWord();
  }

  // Load next word
  Future<bool> _loadNextWord() async {
    try {
      _currentWord = await _databaseService.getRandomWord(_currentLevel);
      if (_currentWord == null) return false;
      
      _scrambleWord();
      _playerAnswer.clear();
      _isGameActive = true;
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error loading word: $e');
      return false;
    }
  }

  // Scramble the current word
  void _scrambleWord() {
    if (_currentWord == null) return;
    
    _scrambledLetters = _currentWord!.word.split('');
    
    // Shuffle until it's different from original
    do {
      _scrambledLetters.shuffle();
    } while (_scrambledLetters.join() == _currentWord!.word && _scrambledLetters.length > 1);
    
    notifyListeners();
  }

  // Add letter to player answer
  void addLetterToAnswer(String letter, int scrambledIndex) {
    if (!_isGameActive || _playerAnswer.length >= _currentWord!.word.length) return;
    
    _playerAnswer.add(letter);
    _scrambledLetters[scrambledIndex] = ''; // Mark as used
    notifyListeners();
  }

  // Remove letter from answer
  void removeLetterFromAnswer(int answerIndex) {
    if (!_isGameActive || answerIndex >= _playerAnswer.length) return;
    
    String letter = _playerAnswer.removeAt(answerIndex);
    
    // Find first empty spot in scrambled letters
    for (int i = 0; i < _scrambledLetters.length; i++) {
      if (_scrambledLetters[i].isEmpty) {
        _scrambledLetters[i] = letter;
        break;
      }
    }
    
    notifyListeners();
  }

  // Check answer
  Future<bool> checkAnswer() async {
    if (!_isGameActive || !isAnswerComplete) return false;
    
    bool correct = isAnswerCorrect;
    
    if (correct) {
      await _handleCorrectAnswer();
    } else {
      await _handleIncorrectAnswer();
    }
    
    return correct;
  }

  // Handle correct answer
  Future<void> _handleCorrectAnswer() async {
    _isGameActive = false;
    
    // Calculate score
    int timeBonus = 0;
    if (_isTimedMode) {
      timeBonus = _timeRemaining * 2;
    } else if (_gameStartTime != null) {
      int timeTaken = DateTime.now().difference(_gameStartTime!).inSeconds;
      timeBonus = max(0, 60 - timeTaken);
    }
    
    int hintPenalty = _hintsUsed * 5;
    int wordScore = _currentLevel.baseScore + timeBonus - hintPenalty;
    _score += max(1, wordScore);
    
    // Save game stats
    await _saveGameStats(true);
    
    notifyListeners();
  }

  // Handle incorrect answer
  Future<void> _handleIncorrectAnswer() async {
    // Reset answer but keep game active
    _playerAnswer.clear();
    
    // Restore scrambled letters
    if (_currentWord != null) {
      _scrambleWord();
    }
    
    notifyListeners();
  }

  // Get hint - reveal a letter
  void getPositionHint() {
    if (!_isGameActive || _currentWord == null || _hintsUsed >= 3) return;
    
    String correctWord = _currentWord!.word.toLowerCase();
    
    // Find first incorrect position or empty position
    for (int i = 0; i < correctWord.length; i++) {
      if (i >= _playerAnswer.length || _playerAnswer[i].toLowerCase() != correctWord[i]) {
        // Remove current letter at this position if exists
        if (i < _playerAnswer.length) {
          removeLetterFromAnswer(i);
        }
        
        // Find the correct letter in scrambled letters
        String correctLetter = correctWord[i];
        for (int j = 0; j < _scrambledLetters.length; j++) {
          if (_scrambledLetters[j].toLowerCase() == correctLetter) {
            addLetterToAnswer(_scrambledLetters[j], j);
            break;
          }
        }
        
        _hintsUsed++;
        notifyListeners();
        break;
      }
    }
  }

  // Get meaning hint
  String? getMeaningHint() {
    if (!_isGameActive || _currentWord == null) return null;
    _hintsUsed++;
    notifyListeners();
    return _currentWord!.meaning;
  }

  // Reshuffle letters
  void reshuffleLetters() {
    if (!_isGameActive) return;
    
    // Collect all unused letters
    List<String> unusedLetters = _scrambledLetters.where((letter) => letter.isNotEmpty).toList();
    unusedLetters.shuffle();
    
    // Put them back
    int unusedIndex = 0;
    for (int i = 0; i < _scrambledLetters.length; i++) {
      if (_scrambledLetters[i].isNotEmpty) {
        _scrambledLetters[i] = unusedLetters[unusedIndex++];
      }
    }
    
    notifyListeners();
  }

  // Timer functions
  void _startTimer() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeRemaining > 0) {
        _timeRemaining--;
        notifyListeners();
      } else {
        _handleTimeUp();
      }
    });
  }

  void _handleTimeUp() {
    _gameTimer?.cancel();
    _isGameActive = false;
    _saveGameStats(false);
    notifyListeners();
  }

  // Save game statistics
  Future<void> _saveGameStats(bool completed) async {
    if (_currentWord == null || _gameStartTime == null) return;
    
    int timeSpent = DateTime.now().difference(_gameStartTime!).inSeconds;
    
    GameStats stats = GameStats(
      level: _currentLevel,
      score: _score,
      timeSpent: timeSpent,
      hintsUsed: _hintsUsed,
      word: _currentWord!.word,
      playedAt: DateTime.now(),
      completed: completed,
    );
    
    await _databaseService.insertGameStats(stats);
  }

  // End game
  void endGame() {
    _gameTimer?.cancel();
    _isGameActive = false;
    if (_currentWord != null && _gameStartTime != null) {
      _saveGameStats(false);
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    super.dispose();
  }
}
