import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/word.dart';
import '../models/game_level.dart';
import '../models/game_stats.dart';
import 'database_service.dart';
import 'high_score_service.dart';

class GameService extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final HighScoreService _highScoreService = HighScoreService();
  
  // Game state
  Word? _currentWord;
  List<String> _scrambledLetters = [];
  List<String> _playerAnswer = [];
  GameLevel _currentLevel = GameLevel.easy;
  int _score = 0;
  int _hintsUsed = 0;
  bool _isGameActive = false;
  bool _isTimedMode = false;
  int _timeRemaining = 0;
  int _wordTimeLimit = 30; // Time limit per word in seconds
  int _wordTimeRemaining = 30;
  Timer? _gameTimer;
  Timer? _wordTimer;
  DateTime? _gameStartTime;
  DateTime? _currentWordStartTime;
  int _wordsCompleted = 0;
  List<String> _completedWords = [];
  bool _isNewHighScore = false;

  // Getters
  Word? get currentWord => _currentWord;
  List<String> get scrambledLetters => List.unmodifiable(_scrambledLetters);
  List<String> get playerAnswer => List.unmodifiable(_playerAnswer);
  GameLevel get currentLevel => _currentLevel;
  int get score => _score;
  int get hintsUsed => _hintsUsed;
  bool get isGameActive => _isGameActive;
  bool get isTimedMode => _isTimedMode;
  int get timeRemaining => _timeRemaining;
  int get wordTimeLimit => _wordTimeLimit;
  int get wordTimeRemaining => _wordTimeRemaining;
  bool get isAnswerComplete => _playerAnswer.length == _currentWord?.word.length;
  bool get isAnswerCorrect => _playerAnswer.join().toLowerCase() == _currentWord?.word.toLowerCase();
  int get wordsCompleted => _wordsCompleted;
  List<String> get completedWords => List.unmodifiable(_completedWords);
  bool get isNewHighScore => _isNewHighScore;

  // Start new game
  Future<bool> startNewGame(GameLevel level, {bool timedMode = false}) async {
    _currentLevel = level;
    _isTimedMode = timedMode;
    _score = 0;
    _hintsUsed = 0;
    _wordsCompleted = 0;
    _completedWords.clear();
    _isNewHighScore = false;
    _gameStartTime = DateTime.now();

    // Set word time limit based on difficulty
    _wordTimeLimit = _getWordTimeLimit(level);

    if (_isTimedMode) {
      _timeRemaining = level.timeLimit;
      _startTimer();
    }

    return await _loadNextWord();
  }

  int _getWordTimeLimit(GameLevel level) {
    switch (level) {
      case GameLevel.easy:
        return 45; // 45 seconds for easy words
      case GameLevel.medium:
        return 35; // 35 seconds for medium words
      case GameLevel.hard:
        return 25; // 25 seconds for hard words
    }
  }

  // Load next word
  Future<bool> _loadNextWord() async {
    try {
      _currentWord = await _databaseService.getRandomWord(_currentLevel);
      if (_currentWord == null) return false;

      // Avoid repeating words in the same session
      while (_completedWords.contains(_currentWord!.word) && _completedWords.length < 20) {
        _currentWord = await _databaseService.getRandomWord(_currentLevel);
        if (_currentWord == null) return false;
      }

      _scrambleWord();
      _playerAnswer.clear();
      _hintsUsed = 0; // Reset hints for new word
      _currentWordStartTime = DateTime.now();
      _isGameActive = true;

      // Start word timer
      _wordTimeRemaining = _wordTimeLimit;
      _startWordTimer();

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error loading word: $e');
      return false;
    }
  }

  // Scramble the current word
  void _scrambleWord() {
    if (_currentWord == null) return;
    
    _scrambledLetters = _currentWord!.word.split('');
    
    // Shuffle until it's different from original
    do {
      _scrambledLetters.shuffle();
    } while (_scrambledLetters.join() == _currentWord!.word && _scrambledLetters.length > 1);
    
    notifyListeners();
  }

  // Add letter to player answer
  void addLetterToAnswer(String letter, int scrambledIndex) {
    if (!_isGameActive || _playerAnswer.length >= _currentWord!.word.length) return;
    
    _playerAnswer.add(letter);
    _scrambledLetters[scrambledIndex] = ''; // Mark as used
    notifyListeners();
  }

  // Remove letter from answer
  void removeLetterFromAnswer(int answerIndex) {
    if (!_isGameActive || answerIndex >= _playerAnswer.length) return;
    
    String letter = _playerAnswer.removeAt(answerIndex);
    
    // Find first empty spot in scrambled letters
    for (int i = 0; i < _scrambledLetters.length; i++) {
      if (_scrambledLetters[i].isEmpty) {
        _scrambledLetters[i] = letter;
        break;
      }
    }
    
    notifyListeners();
  }

  // Check answer
  Future<bool> checkAnswer() async {
    if (!_isGameActive || !isAnswerComplete) return false;
    
    bool correct = isAnswerCorrect;
    
    if (correct) {
      await _handleCorrectAnswer();
    } else {
      await _handleIncorrectAnswer();
    }
    
    return correct;
  }

  // Handle correct answer
  Future<void> _handleCorrectAnswer() async {
    if (_currentWord == null) return;

    // Stop word timer
    _wordTimer?.cancel();

    // Add to completed words
    _completedWords.add(_currentWord!.word);
    _wordsCompleted++;

    // Calculate score for this word
    int timeBonus = _wordTimeRemaining * 2; // Bonus for remaining time
    int hintPenalty = _hintsUsed * 5;
    int wordScore = _currentLevel.baseScore + timeBonus - hintPenalty;
    _score += max(1, wordScore);

    // Save individual word stats
    await _saveWordStats(true);

    notifyListeners();
  }

  // Load next word after correct answer
  Future<bool> loadNextWord() async {
    return await _loadNextWord();
  }

  // Handle incorrect answer
  Future<void> _handleIncorrectAnswer() async {
    // Reset answer but keep game active
    _playerAnswer.clear();
    
    // Restore scrambled letters
    if (_currentWord != null) {
      _scrambleWord();
    }
    
    notifyListeners();
  }

  // Get hint - reveal a letter
  void getPositionHint() {
    if (!_isGameActive || _currentWord == null || _hintsUsed >= 3) return;
    
    String correctWord = _currentWord!.word.toLowerCase();
    
    // Find first incorrect position or empty position
    for (int i = 0; i < correctWord.length; i++) {
      if (i >= _playerAnswer.length || _playerAnswer[i].toLowerCase() != correctWord[i]) {
        // Remove current letter at this position if exists
        if (i < _playerAnswer.length) {
          removeLetterFromAnswer(i);
        }
        
        // Find the correct letter in scrambled letters
        String correctLetter = correctWord[i];
        for (int j = 0; j < _scrambledLetters.length; j++) {
          if (_scrambledLetters[j].toLowerCase() == correctLetter) {
            addLetterToAnswer(_scrambledLetters[j], j);
            break;
          }
        }
        
        _hintsUsed++;
        notifyListeners();
        break;
      }
    }
  }

  // Get meaning hint
  String? getMeaningHint() {
    if (!_isGameActive || _currentWord == null) return null;
    _hintsUsed++;
    notifyListeners();
    return _currentWord!.meaning;
  }

  // Reshuffle letters
  void reshuffleLetters() {
    if (!_isGameActive) return;
    
    // Collect all unused letters
    List<String> unusedLetters = _scrambledLetters.where((letter) => letter.isNotEmpty).toList();
    unusedLetters.shuffle();
    
    // Put them back
    int unusedIndex = 0;
    for (int i = 0; i < _scrambledLetters.length; i++) {
      if (_scrambledLetters[i].isNotEmpty) {
        _scrambledLetters[i] = unusedLetters[unusedIndex++];
      }
    }
    
    notifyListeners();
  }

  // Timer functions
  void _startTimer() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeRemaining > 0) {
        _timeRemaining--;
        notifyListeners();
      } else {
        _handleTimeUp();
      }
    });
  }

  void _startWordTimer() {
    _wordTimer?.cancel();
    _wordTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_wordTimeRemaining > 0) {
        _wordTimeRemaining--;
        notifyListeners();
      } else {
        _handleWordTimeUp();
      }
    });
  }

  void _handleTimeUp() {
    _gameTimer?.cancel();
    _wordTimer?.cancel();
    _isGameActive = false;
    // Save final game session stats
    _saveFinalGameStats();
    notifyListeners();
  }

  void _handleWordTimeUp() {
    _wordTimer?.cancel();
    _isGameActive = false;
    // Check if this is a new high score
    _checkHighScore();
    // Save final game session stats
    _saveFinalGameStats();
    notifyListeners();
  }

  // Save individual word statistics
  Future<void> _saveWordStats(bool completed) async {
    if (_currentWord == null || _currentWordStartTime == null) return;

    int timeSpent = DateTime.now().difference(_currentWordStartTime!).inSeconds;

    GameStats stats = GameStats(
      level: _currentLevel,
      score: _score,
      timeSpent: timeSpent,
      hintsUsed: _hintsUsed,
      word: _currentWord!.word,
      playedAt: DateTime.now(),
      completed: completed,
    );

    await _databaseService.insertGameStats(stats);
  }

  // Save final game session statistics
  Future<void> _saveFinalGameStats() async {
    if (_gameStartTime == null) return;

    int totalTimeSpent = DateTime.now().difference(_gameStartTime!).inSeconds;

    // Save a summary record for the entire game session
    GameStats sessionStats = GameStats(
      level: _currentLevel,
      score: _score,
      timeSpent: totalTimeSpent,
      hintsUsed: 0, // This represents the session, not individual word hints
      word: 'SESSION_${_wordsCompleted}_WORDS', // Special marker for session records
      playedAt: DateTime.now(),
      completed: false, // Session ended due to time up
    );

    await _databaseService.insertGameStats(sessionStats);
  }

  // Check if current score is a new high score
  Future<void> _checkHighScore() async {
    try {
      _isNewHighScore = await _highScoreService.saveHighScore(_currentLevel, _score);
    } catch (e) {
      debugPrint('Error checking high score: $e');
      _isNewHighScore = false;
    }
  }

  // End game
  void endGame() {
    _gameTimer?.cancel();
    _wordTimer?.cancel();
    _isGameActive = false;
    if (_gameStartTime != null) {
      _checkHighScore();
      _saveFinalGameStats();
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _wordTimer?.cancel();
    super.dispose();
  }
}
