import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/settings_service.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài đặt'),
      ),
      body: Consumer<SettingsService>(
        builder: (context, settings, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Theme section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Giao diện',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Chế độ ban đêm'),
                        subtitle: const Text('Bảo vệ mắt khi chơi trong môi trường tối'),
                        value: settings.isDarkMode,
                        onChanged: (_) => settings.toggleDarkMode(),
                        secondary: Icon(
                          settings.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Audio section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Âm thanh & Rung',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Âm thanh'),
                        subtitle: const Text('Bật/tắt hiệu ứng âm thanh'),
                        value: settings.isSoundEnabled,
                        onChanged: (_) => settings.toggleSound(),
                        secondary: Icon(
                          settings.isSoundEnabled ? Icons.volume_up : Icons.volume_off,
                        ),
                      ),
                      SwitchListTile(
                        title: const Text('Rung'),
                        subtitle: const Text('Rung khi có phản hồi'),
                        value: settings.isVibrationEnabled,
                        onChanged: (_) => settings.toggleVibration(),
                        secondary: const Icon(Icons.vibration),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Game settings section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Trò chơi',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Gợi ý'),
                        subtitle: const Text('Cho phép sử dụng gợi ý trong game'),
                        value: settings.areHintsEnabled,
                        onChanged: (_) => settings.toggleHints(),
                        secondary: const Icon(Icons.lightbulb_outline),
                      ),
                      SwitchListTile(
                        title: const Text('Chế độ có thời gian mặc định'),
                        subtitle: const Text('Tự động bật chế độ có thời gian khi bắt đầu game'),
                        value: settings.isTimedModeDefault,
                        onChanged: (_) => settings.toggleTimedModeDefault(),
                        secondary: const Icon(Icons.timer),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // About section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Về ứng dụng',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        leading: const Icon(Icons.info_outline),
                        title: const Text('Phiên bản'),
                        subtitle: const Text('1.0.0'),
                        onTap: () => _showAboutDialog(context),
                      ),
                      ListTile(
                        leading: const Icon(Icons.refresh),
                        title: const Text('Khôi phục cài đặt mặc định'),
                        subtitle: const Text('Đặt lại tất cả cài đặt về mặc định'),
                        onTap: () => _showResetDialog(context, settings),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Unscramble',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.games, size: 48),
      children: [
        const Text('Trò chơi ô chữ tiếng Anh giúp cải thiện vốn từ vựng.'),
        const SizedBox(height: 16),
        const Text('Phát triển bởi Flutter'),
      ],
    );
  }

  void _showResetDialog(BuildContext context, SettingsService settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Khôi phục cài đặt'),
        content: const Text('Bạn có chắc chắn muốn đặt lại tất cả cài đặt về mặc định?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              settings.resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã khôi phục cài đặt mặc định'),
                ),
              );
            },
            child: const Text('Đồng ý'),
          ),
        ],
      ),
    );
  }
}
