// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:unscramble/main.dart';
import 'package:unscramble/services/settings_service.dart';

void main() {
  testWidgets('App starts and shows main menu', (WidgetTester tester) async {
    // Initialize settings service
    final settingsService = SettingsService();
    await settingsService.init();

    // Build our app and trigger a frame.
    await tester.pumpWidget(UnscrambleApp(settingsService: settingsService));

    // Verify that the main menu is displayed
    expect(find.text('UNSCRAMBLE'), findsOneWidget);
    expect(find.text('<PERSON><PERSON><PERSON> chơi ô chữ tiếng <PERSON>'), findsOneWidget);
  });
}
