import 'dart:async';
import 'dart:math';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/word.dart';
import '../models/game_stats.dart';
import '../models/game_level.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'unscramble.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create words table
    await db.execute('''
      CREATE TABLE words(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        word TEXT NOT NULL UNIQUE,
        meaning TEXT NOT NULL,
        length INTEGER NOT NULL,
        category TEXT DEFAULT 'general',
        example TEXT,
        pronunciation TEXT
      )
    ''');

    // Create game_stats table
    await db.execute('''
      CREATE TABLE game_stats(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level TEXT NOT NULL,
        score INTEGER NOT NULL,
        timeSpent INTEGER NOT NULL,
        hintsUsed INTEGER NOT NULL,
        word TEXT NOT NULL,
        playedAt INTEGER NOT NULL,
        completed INTEGER NOT NULL
      )
    ''');

    // Insert sample words
    await _insertSampleWords(db);
  }

  Future<void> _insertSampleWords(Database db) async {
    final sampleWords = [
      // Easy words (3-4 letters)
      Word(word: 'cat', meaning: 'con mèo', length: 3,
           example: 'The cat is sleeping on the sofa.', pronunciation: '/kæt/'),
      Word(word: 'dog', meaning: 'con chó', length: 3,
           example: 'My dog loves to play in the park.', pronunciation: '/dɔːɡ/'),
      Word(word: 'sun', meaning: 'mặt trời', length: 3,
           example: 'The sun is shining brightly today.', pronunciation: '/sʌn/'),
      Word(word: 'moon', meaning: 'mặt trăng', length: 4,
           example: 'The moon looks beautiful tonight.', pronunciation: '/muːn/'),
      Word(word: 'book', meaning: 'quyển sách', length: 4,
           example: 'I am reading an interesting book.', pronunciation: '/bʊk/'),
      Word(word: 'tree', meaning: 'cây cối', length: 4,
           example: 'The tree provides shade in summer.', pronunciation: '/triː/'),
      Word(word: 'fish', meaning: 'con cá', length: 4,
           example: 'We caught many fish in the lake.', pronunciation: '/fɪʃ/'),
      Word(word: 'bird', meaning: 'con chim', length: 4,
           example: 'A colorful bird is singing outside.', pronunciation: '/bɜːrd/'),
      Word(word: 'home', meaning: 'nhà', length: 4,
           example: 'There is no place like home.', pronunciation: '/hoʊm/'),
      Word(word: 'love', meaning: 'tình yêu', length: 4,
           example: 'Love makes the world go round.', pronunciation: '/lʌv/'),

      // Medium words (5-6 letters)
      Word(word: 'house', meaning: 'ngôi nhà', length: 5,
           example: 'We bought a new house last year.', pronunciation: '/haʊs/'),
      Word(word: 'water', meaning: 'nước', length: 5,
           example: 'Please drink more water every day.', pronunciation: '/ˈwɔːtər/'),
      Word(word: 'happy', meaning: 'vui vẻ', length: 5,
           example: 'She looks very happy today.', pronunciation: '/ˈhæpi/'),
      Word(word: 'friend', meaning: 'bạn bè', length: 6,
           example: 'A true friend is always there for you.', pronunciation: '/frend/'),
      Word(word: 'school', meaning: 'trường học', length: 6,
           example: 'Children go to school to learn.', pronunciation: '/skuːl/'),
      Word(word: 'family', meaning: 'gia đình', length: 6,
           example: 'Family is the most important thing.', pronunciation: '/ˈfæməli/'),
      Word(word: 'flower', meaning: 'bông hoa', length: 6,
           example: 'The flower smells very sweet.', pronunciation: '/ˈflaʊər/'),
      Word(word: 'garden', meaning: 'khu vườn', length: 6,
           example: 'We grow vegetables in our garden.', pronunciation: '/ˈɡɑːrdən/'),
      Word(word: 'music', meaning: 'âm nhạc', length: 5,
           example: 'Music can heal the soul.', pronunciation: '/ˈmjuːzɪk/'),
      Word(word: 'dream', meaning: 'giấc mơ', length: 5,
           example: 'Follow your dreams and never give up.', pronunciation: '/driːm/'),

      // Hard words (7+ letters)
      Word(word: 'beautiful', meaning: 'xinh đẹp', length: 9,
           example: 'The sunset is absolutely beautiful.', pronunciation: '/ˈbjuːtɪfəl/'),
      Word(word: 'computer', meaning: 'máy tính', length: 8,
           example: 'I use my computer for work every day.', pronunciation: '/kəmˈpjuːtər/'),
      Word(word: 'elephant', meaning: 'con voi', length: 8,
           example: 'The elephant is the largest land animal.', pronunciation: '/ˈeləfənt/'),
      Word(word: 'rainbow', meaning: 'cầu vồng', length: 7,
           example: 'A rainbow appeared after the rain.', pronunciation: '/ˈreɪnboʊ/'),
      Word(word: 'butterfly', meaning: 'con bướm', length: 9,
           example: 'The butterfly landed on the flower.', pronunciation: '/ˈbʌtərflaɪ/'),
      Word(word: 'mountain', meaning: 'núi', length: 8,
           example: 'We climbed the mountain yesterday.', pronunciation: '/ˈmaʊntən/'),
      Word(word: 'adventure', meaning: 'cuộc phiêu lưu', length: 9,
           example: 'Life is a great adventure.', pronunciation: '/ədˈventʃər/'),
      Word(word: 'knowledge', meaning: 'kiến thức', length: 9,
           example: 'Knowledge is power.', pronunciation: '/ˈnɑːlɪdʒ/'),
      Word(word: 'wonderful', meaning: 'tuyệt vời', length: 9,
           example: 'What a wonderful day it is!', pronunciation: '/ˈwʌndərfəl/'),
      Word(word: 'education', meaning: 'giáo dục', length: 9,
           example: 'Education is the key to success.', pronunciation: '/ˌedʒuˈkeɪʃən/'),
    ];

    for (Word word in sampleWords) {
      await db.insert('words', word.toMap());
    }
  }

  // Word operations
  Future<List<Word>> getWordsByLevel(GameLevel level) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'words',
      where: 'length >= ? AND length <= ?',
      whereArgs: [level.minLength, level.maxLength],
    );

    return List.generate(maps.length, (i) => Word.fromMap(maps[i]));
  }

  Future<Word?> getRandomWord(GameLevel level) async {
    final words = await getWordsByLevel(level);
    if (words.isEmpty) return null;
    
    final random = Random();
    return words[random.nextInt(words.length)];
  }

  Future<int> insertWord(Word word) async {
    final db = await database;
    return await db.insert('words', word.toMap());
  }

  // Game stats operations
  Future<int> insertGameStats(GameStats stats) async {
    final db = await database;
    return await db.insert('game_stats', stats.toMap());
  }

  Future<List<GameStats>> getGameStats({int limit = 10}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'game_stats',
      orderBy: 'playedAt DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) => GameStats.fromMap(maps[i]));
  }

  Future<int> getBestScore() async {
    final db = await database;
    final result = await db.rawQuery('SELECT MAX(score) as maxScore FROM game_stats WHERE completed = 1');
    return result.first['maxScore'] as int? ?? 0;
  }

  Future<int> getTotalGamesPlayed() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM game_stats');
    return result.first['count'] as int? ?? 0;
  }

  Future<List<String>> getLearnedWords() async {
    final db = await database;
    final result = await db.rawQuery('SELECT DISTINCT word FROM game_stats WHERE completed = 1');
    return result.map((row) => row['word'] as String).toList();
  }

  Future<List<Word>> getLearnedWordsWithDetails() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT DISTINCT w.* FROM words w
      INNER JOIN game_stats gs ON w.word = gs.word
      WHERE gs.completed = 1
      ORDER BY w.word ASC
    ''');
    return result.map((row) => Word.fromMap(row)).toList();
  }

  Future<List<Word>> searchWords(String query) async {
    final db = await database;
    final result = await db.query(
      'words',
      where: 'word LIKE ? OR meaning LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'word ASC',
    );
    return result.map((row) => Word.fromMap(row)).toList();
  }

  Future<Word?> getWordByText(String wordText) async {
    final db = await database;
    final result = await db.query(
      'words',
      where: 'word = ?',
      whereArgs: [wordText.toLowerCase()],
      limit: 1,
    );

    if (result.isNotEmpty) {
      return Word.fromMap(result.first);
    }
    return null;
  }
}
