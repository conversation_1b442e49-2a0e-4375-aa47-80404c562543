import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_level.dart';
import '../services/game_service.dart';
import '../services/settings_service.dart';
import 'game_screen.dart';
import 'settings_screen.dart';
import 'leaderboard_screen.dart';

class MainMenuScreen extends StatelessWidget {
  const MainMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: <PERSON>umn(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Title
                Icon(
                  Icons.games,
                  size: 80,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'UNSCRAMBLE',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Trò chơi ô chữ tiếng Anh',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 48),

                // Difficulty Level Cards
                Text(
                  'Chọn độ khó',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 24),

                // Easy Level
                _buildLevelCard(
                  context,
                  level: GameLevel.easy,
                  icon: Icons.sentiment_satisfied,
                  color: Colors.green,
                  description: '${GameLevel.easy.minLength}-${GameLevel.easy.maxLength} chữ cái • ${GameLevel.easy.timeLimit}s',
                ),
                const SizedBox(height: 16),

                // Medium Level
                _buildLevelCard(
                  context,
                  level: GameLevel.medium,
                  icon: Icons.sentiment_neutral,
                  color: Colors.orange,
                  description: '${GameLevel.medium.minLength}-${GameLevel.medium.maxLength} chữ cái • ${GameLevel.medium.timeLimit}s',
                ),
                const SizedBox(height: 16),

                // Hard Level
                _buildLevelCard(
                  context,
                  level: GameLevel.hard,
                  icon: Icons.sentiment_very_dissatisfied,
                  color: Colors.red,
                  description: '${GameLevel.hard.minLength}-${GameLevel.hard.maxLength} chữ cái • ${GameLevel.hard.timeLimit}s',
                ),

                const SizedBox(height: 48),

                // Bottom buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildMenuButton(
                      context,
                      icon: Icons.leaderboard,
                      label: 'Bảng xếp hạng',
                      onTap: () => _navigateToLeaderboard(context),
                    ),
                    _buildMenuButton(
                      context,
                      icon: Icons.settings,
                      label: 'Cài đặt',
                      onTap: () => _navigateToSettings(context),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLevelCard(
    BuildContext context, {
    required GameLevel level,
    required IconData icon,
    required Color color,
    required String description,
  }) {
    return Card(
      child: InkWell(
        onTap: () => _startGame(context, level),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      level.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 32,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _startGame(BuildContext context, GameLevel level) {
    final settingsService = Provider.of<SettingsService>(context, listen: false);
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GameScreen(
          level: level,
          isTimedMode: settingsService.isTimedModeDefault,
        ),
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _navigateToLeaderboard(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LeaderboardScreen(),
      ),
    );
  }
}
