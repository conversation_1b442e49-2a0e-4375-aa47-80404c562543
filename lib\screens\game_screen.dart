import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_level.dart';
import '../services/game_service.dart';
import '../services/settings_service.dart';
import '../widgets/letter_tile.dart';
import '../widgets/answer_slot.dart';
import '../widgets/game_timer.dart';

class GameScreen extends StatefulWidget {
  final GameLevel level;
  final bool isTimedMode;

  const GameScreen({
    super.key,
    required this.level,
    this.isTimedMode = false,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late AnimationController _shakeController;
  late AnimationController _successController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _successAnimation;

  @override
  void initState() {
    super.initState();
    
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _successController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
    
    _successAnimation = Tween<double>(
      begin: 1,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));

    // Start the game
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startGame();
    });
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _successController.dispose();
    super.dispose();
  }

  void _startGame() async {
    final gameService = Provider.of<GameService>(context, listen: false);
    final success = await gameService.startNewGame(widget.level, timedMode: widget.isTimedMode);
    
    if (!success) {
      _showErrorDialog('Không thể tải từ vựng. Vui lòng thử lại.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.level.name} - Unscramble'),
        actions: [
          if (widget.isTimedMode)
            Consumer<GameService>(
              builder: (context, gameService, child) {
                return GameTimer(timeRemaining: gameService.timeRemaining);
              },
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _restartGame,
          ),
        ],
      ),
      body: Consumer<GameService>(
        builder: (context, gameService, child) {
          if (gameService.currentWord == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Score and hints info
                _buildGameInfo(gameService),
                const SizedBox(height: 24),

                // Word meaning (if hint is used)
                if (gameService.hintsUsed > 0)
                  _buildMeaningHint(gameService),

                const Spacer(),

                // Answer area
                AnimatedBuilder(
                  animation: _shakeAnimation,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(_shakeAnimation.value, 0),
                      child: _buildAnswerArea(gameService),
                    );
                  },
                ),

                const SizedBox(height: 32),

                // Scrambled letters
                AnimatedBuilder(
                  animation: _successAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _successAnimation.value,
                      child: _buildScrambledLetters(gameService),
                    );
                  },
                ),

                const Spacer(),

                // Action buttons
                _buildActionButtons(gameService),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildGameInfo(GameService gameService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Điểm',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${gameService.score}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
              VerticalDivider(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                thickness: 1,
              ),
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Gợi ý',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${gameService.hintsUsed}/3',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: gameService.hintsUsed >= 3 ? Colors.red : null,
                      ),
                    ),
                  ],
                ),
              ),
              VerticalDivider(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                thickness: 1,
              ),
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Từ',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${gameService.currentWord?.word.length} chữ',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMeaningHint(GameService gameService) {
    return Card(
      color: Theme.of(context).colorScheme.primaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.lightbulb,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Nghĩa: ${gameService.currentWord?.meaning}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnswerArea(GameService gameService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 4,
        runSpacing: 8,
        children: List.generate(
          gameService.currentWord!.word.length,
          (index) => AnswerSlot(
            letter: index < gameService.playerAnswer.length
                ? gameService.playerAnswer[index]
                : null,
            onTap: index < gameService.playerAnswer.length
                ? () => gameService.removeLetterFromAnswer(index)
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildScrambledLetters(GameService gameService) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      runSpacing: 8,
      children: List.generate(
        gameService.scrambledLetters.length,
        (index) => LetterTile(
          letter: gameService.scrambledLetters[index],
          onTap: gameService.scrambledLetters[index].isNotEmpty
              ? () => gameService.addLetterToAnswer(
                    gameService.scrambledLetters[index], 
                    index,
                  )
              : null,
        ),
      ),
    );
  }

  Widget _buildActionButtons(GameService gameService) {
    final settingsService = Provider.of<SettingsService>(context);
    
    return Column(
      children: [
        // Check answer button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: gameService.isAnswerComplete ? _checkAnswer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              'Kiểm tra đáp án',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        // Action buttons row
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 3 - 8,
              child: OutlinedButton.icon(
                onPressed: gameService.isGameActive ? gameService.reshuffleLetters : null,
                icon: const Icon(Icons.shuffle, size: 18),
                label: const Text('Xáo trộn', style: TextStyle(fontSize: 12)),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                ),
              ),
            ),
            if (settingsService.areHintsEnabled)
              SizedBox(
                width: (MediaQuery.of(context).size.width - 48) / 3 - 8,
                child: OutlinedButton.icon(
                  onPressed: gameService.hintsUsed < 3 && gameService.isGameActive
                      ? gameService.getPositionHint
                      : null,
                  icon: const Icon(Icons.lightbulb_outline, size: 18),
                  label: const Text('Gợi ý', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  ),
                ),
              ),
            if (settingsService.areHintsEnabled)
              SizedBox(
                width: (MediaQuery.of(context).size.width - 48) / 3 - 8,
                child: OutlinedButton.icon(
                  onPressed: gameService.hintsUsed < 3 && gameService.isGameActive
                      ? () => gameService.getMeaningHint()
                      : null,
                  icon: const Icon(Icons.help_outline, size: 18),
                  label: const Text('Nghĩa', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  void _checkAnswer() async {
    final gameService = Provider.of<GameService>(context, listen: false);
    final isCorrect = await gameService.checkAnswer();
    
    if (isCorrect) {
      _successController.forward().then((_) {
        _successController.reverse();
        _showSuccessDialog();
      });
    } else {
      _shakeController.forward().then((_) {
        _shakeController.reverse();
      });
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Chính xác!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Bạn đã giải đúng từ: "${Provider.of<GameService>(context, listen: false).currentWord?.word}"'),
            const SizedBox(height: 8),
            Text('Điểm: ${Provider.of<GameService>(context, listen: false).score}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startGame();
            },
            child: const Text('Tiếp tục'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Về menu'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lỗi'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _restartGame() {
    _startGame();
  }
}
