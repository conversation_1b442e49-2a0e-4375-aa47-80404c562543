import 'game_level.dart';

class GameStats {
  final int? id;
  final GameLevel level;
  final int score;
  final int timeSpent; // seconds
  final int hintsUsed;
  final String word;
  final DateTime playedAt;
  final bool completed;

  GameStats({
    this.id,
    required this.level,
    required this.score,
    required this.timeSpent,
    required this.hintsUsed,
    required this.word,
    required this.playedAt,
    required this.completed,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'level': level.name,
      'score': score,
      'timeSpent': timeSpent,
      'hintsUsed': hintsUsed,
      'word': word,
      'playedAt': playedAt.millisecondsSinceEpoch,
      'completed': completed ? 1 : 0,
    };
  }

  factory GameStats.fromMap(Map<String, dynamic> map) {
    return GameStats(
      id: map['id'],
      level: GameLevel.values.firstWhere((e) => e.name == map['level']),
      score: map['score'],
      timeSpent: map['timeSpent'],
      hintsUsed: map['hintsUsed'],
      word: map['word'],
      playedAt: DateTime.fromMillisecondsSinceEpoch(map['playedAt']),
      completed: map['completed'] == 1,
    );
  }
}

class PlayerProgress {
  final int totalGamesPlayed;
  final int totalWordsLearned;
  final int bestScore;
  final Map<GameLevel, int> levelStats;
  final List<String> learnedWords;

  PlayerProgress({
    required this.totalGamesPlayed,
    required this.totalWordsLearned,
    required this.bestScore,
    required this.levelStats,
    required this.learnedWords,
  });

  factory PlayerProgress.empty() {
    return PlayerProgress(
      totalGamesPlayed: 0,
      totalWordsLearned: 0,
      bestScore: 0,
      levelStats: {
        GameLevel.easy: 0,
        GameLevel.medium: 0,
        GameLevel.hard: 0,
      },
      learnedWords: [],
    );
  }
}
