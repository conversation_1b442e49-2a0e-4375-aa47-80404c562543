import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class TextToSpeechService {
  static final TextToSpeechService _instance = TextToSpeechService._internal();
  factory TextToSpeechService() => _instance;
  TextToSpeechService._internal();

  static const MethodChannel _channel = MethodChannel('text_to_speech');
  
  bool _isAvailable = false;
  bool _isSpeaking = false;

  bool get isAvailable => _isAvailable;
  bool get isSpeaking => _isSpeaking;

  Future<void> initialize() async {
    try {
      _isAvailable = await _channel.invokeMethod('initialize') ?? false;
    } catch (e) {
      debugPrint('TTS initialization failed: $e');
      _isAvailable = false;
    }
  }

  Future<void> speak(String text, {String language = 'en-US'}) async {
    if (!_isAvailable || text.isEmpty) return;

    try {
      _isSpeaking = true;
      await _channel.invokeMethod('speak', {
        'text': text,
        'language': language,
      });
    } catch (e) {
      debugPrint('TTS speak failed: $e');
    } finally {
      _isSpeaking = false;
    }
  }

  Future<void> stop() async {
    if (!_isAvailable) return;

    try {
      await _channel.invokeMethod('stop');
      _isSpeaking = false;
    } catch (e) {
      debugPrint('TTS stop failed: $e');
    }
  }

  Future<void> setRate(double rate) async {
    if (!_isAvailable) return;

    try {
      await _channel.invokeMethod('setRate', {'rate': rate});
    } catch (e) {
      debugPrint('TTS setRate failed: $e');
    }
  }

  Future<void> setPitch(double pitch) async {
    if (!_isAvailable) return;

    try {
      await _channel.invokeMethod('setPitch', {'pitch': pitch});
    } catch (e) {
      debugPrint('TTS setPitch failed: $e');
    }
  }

  // Fallback method for web/desktop platforms
  Future<void> speakFallback(String text) async {
    if (kIsWeb) {
      // For web, we could use Web Speech API through JS interop
      // For now, just show a message
      debugPrint('TTS not available on web platform. Text: $text');
    } else {
      // For desktop platforms, could integrate with system TTS
      debugPrint('TTS not available on this platform. Text: $text');
    }
  }

  void dispose() {
    stop();
  }
}
